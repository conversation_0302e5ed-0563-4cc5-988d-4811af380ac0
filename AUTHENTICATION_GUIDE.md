# Jobs Portal Authentication System

## Overview

This document describes the complete authentication system implemented for the Jobs Portal using NextAuth.js with MongoDB Atlas integration. The system supports role-based authentication for both Job Seekers and Employers.

## Features Implemented

### ✅ Sprint 1: User Authentication (COMPLETED)

- **User Registration**: Secure user registration with role selection
- **User Login**: Credential-based authentication using NextAuth.js
- **Role-Based Access Control**: Separate dashboards for Job Seekers and Employers
- **Session Management**: Persistent sessions with JWT tokens
- **Protected Routes**: Middleware-based route protection
- **Password Security**: Bcrypt password hashing
- **MongoDB Integration**: User data stored in MongoDB Atlas

## Architecture

### Database Configuration
- **Database**: `auth` (MongoDB Atlas)
- **Collection**: `users`
- **Connection**: MongoDB Atlas cluster with connection string in environment variables

### Authentication Flow
1. User registers with email, password, name, and role
2. Password is hashed using bcrypt
3. User data is stored in MongoDB
4. NextAuth.js handles session management
5. JWT tokens contain user role for authorization
6. Middleware protects routes based on authentication and role

## File Structure

```
src/
├── app/
│   ├── api/
│   │   └── auth/
│   │       ├── [...nextauth]/route.ts    # NextAuth.js API routes
│   │       └── register/route.ts         # User registration API
│   ├── auth/
│   │   ├── signin/page.tsx              # Sign-in page
│   │   └── signup/page.tsx              # Sign-up page
│   ├── dashboard/
│   │   ├── employer/page.tsx            # Employer dashboard
│   │   ├── job-seeker/page.tsx          # Job seeker dashboard
│   │   └── page.tsx                     # Dashboard redirect
│   ├── jobs/page.tsx                    # Jobs listing (placeholder)
│   ├── layout.tsx                       # Root layout with providers
│   └── page.tsx                         # Home page
├── components/
│   ├── auth/
│   │   └── ProtectedRoute.tsx           # Route protection component
│   ├── layout/
│   │   └── Navigation.tsx               # Navigation with auth state
│   └── providers/
│       └── SessionProvider.tsx          # NextAuth session provider
├── lib/
│   ├── auth.ts                          # NextAuth configuration
│   ├── mongodb.ts                       # MongoDB connection
│   └── userService.ts                   # User database operations
├── types/
│   ├── next-auth.d.ts                   # NextAuth type extensions
│   └── user.ts                          # User type definitions
└── middleware.ts                        # Route protection middleware
```

## Environment Variables

```env
# NextAuth.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# MongoDB Configuration
MONGODB_URI=mongodb+srv://mohamed:<EMAIL>/auth
```

## API Endpoints

### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "job_seeker" | "employer"
}
```

**Response (201):**
```json
{
  "message": "User created successfully",
  "user": {
    "id": "user_id",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "job_seeker"
  }
}
```

### NextAuth.js Endpoints
- `GET/POST /api/auth/[...nextauth]` - NextAuth.js authentication endpoints
- `GET /api/auth/session` - Get current session
- `POST /api/auth/signin` - Sign in user
- `POST /api/auth/signout` - Sign out user

## User Roles

### Job Seeker
- Can browse and search jobs
- Can apply to job postings
- Has access to job seeker dashboard
- Can manage their profile and applications

### Employer
- Can post and manage job listings
- Can view and manage applications
- Has access to employer dashboard
- Can manage company profile

## Route Protection

### Public Routes
- `/` - Home page
- `/jobs` - Job listings
- `/auth/signin` - Sign in page
- `/auth/signup` - Sign up page

### Protected Routes
- `/dashboard/*` - Requires authentication
- `/dashboard/job-seeker/*` - Requires job_seeker role
- `/dashboard/employer/*` - Requires employer role

## Security Features

1. **Password Hashing**: All passwords are hashed using bcrypt with salt rounds of 12
2. **JWT Tokens**: Secure session management with signed JWT tokens
3. **Role-Based Access**: Middleware enforces role-based route access
4. **Input Validation**: Server-side validation for all user inputs
5. **CSRF Protection**: NextAuth.js provides built-in CSRF protection

## Testing

The authentication system has been tested with:
- ✅ Job Seeker registration
- ✅ Employer registration
- ✅ Duplicate email validation
- ✅ Invalid role validation
- ✅ Password hashing verification
- ✅ MongoDB Atlas connection
- ✅ Session management
- ✅ Route protection

## Next Steps (Future Sprints)

### Sprint 2: Employer Features - Job Management
- Job posting creation and management
- Application review system
- Employer profile management

### Sprint 3: Job Seeker Features - Job Discovery
- Job search and filtering
- Job application system
- Profile and resume management

## Usage Examples

### Register a new user
```javascript
const response = await fetch('/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'password123',
    role: 'job_seeker'
  })
});
```

### Sign in a user
```javascript
import { signIn } from 'next-auth/react';

const result = await signIn('credentials', {
  email: '<EMAIL>',
  password: 'password123',
  redirect: false
});
```

### Protect a component
```jsx
import ProtectedRoute from '@/components/auth/ProtectedRoute';

function EmployerPage() {
  return (
    <ProtectedRoute requiredRole="employer">
      <div>Employer-only content</div>
    </ProtectedRoute>
  );
}
```

## Troubleshooting

### Common Issues
1. **MongoDB Connection**: Ensure MONGODB_URI is correctly set in environment variables
2. **NextAuth Secret**: Make sure NEXTAUTH_SECRET is set for production
3. **Role Mismatch**: Check that user roles match the required route permissions

### Development Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables in `.env.local`
4. Start development server: `npm run dev`
5. Access the application at `http://localhost:3000`
